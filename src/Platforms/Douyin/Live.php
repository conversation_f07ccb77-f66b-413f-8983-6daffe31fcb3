<?php

declare(strict_types=1);

namespace LiveStream\Platforms\Douyin;

use LiveStream\Enum\Quality;
use LiveStream\Contracts\LiveInterface;

class Live implements LiveInterface
{
    public function __construct(
        protected int $status,
        protected string $title,
        protected string $anchorName,
        protected string $roomId,
        protected array $streamUrl,
        protected string $userCountStr
    ) {}

    public function isLive(): bool
    {
        return $this->status === 2;
    }

    public function getFlvUrl(?Quality $quality = null): ?string
    {
        // 如果指定了具体的清晰度，直接返回对应的URL
        if ($quality !== null) {
            return $this->streamUrl['flv_pull_url'][$quality->value] ?? null;
        }

        // 如果quality为null，使用智能降级逻辑
        return $this->findAvailableUrl('flv_pull_url');
    }

    public function getHlsUrl(?Quality $quality = null): ?string
    {
        // 如果指定了具体的清晰度，直接返回对应的URL
        if ($quality !== null) {
            return $this->streamUrl['hls_pull_url_map'][$quality->value] ?? null;
        }

        // 如果quality为null，使用智能降级逻辑
        return $this->findAvailableUrl('hls_pull_url_map');
    }

    public function getStreamUrl(): array
    {
        return $this->streamUrl;
    }

    public function getAnchorName(): string
    {
        return $this->anchorName;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getRoomId(): string
    {
        return $this->roomId;
    }

    public function getUserCountStr(): string
    {
        return $this->userCountStr;
    }

    /**
     * 智能降级：按优先级顺序查找可用的流URL
     *
     * @param string $urlType URL类型：'flv_pull_url' 或 'hls_pull_url_map'
     * @return string|null 找到的第一个可用URL，如果都不可用则返回null
     */
    private function findAvailableUrl(string $urlType): ?string
    {
        // 检查streamUrl结构是否有效
        if (!isset($this->streamUrl[$urlType]) || !is_array($this->streamUrl[$urlType])) {
            return null;
        }

        $urlMap = $this->streamUrl[$urlType];

        // 按优先级顺序尝试每个清晰度
        foreach (Quality::getPriorityOrder() as $quality) {
            $url = $urlMap[$quality->value] ?? null;

            // 检查URL是否存在且不为空字符串
            if ($url !== null && $url !== '') {
                return $url;
            }
        }

        return null;
    }
}
