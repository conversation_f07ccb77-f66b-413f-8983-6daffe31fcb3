<?php

declare(strict_types=1);

namespace LiveStream\Platforms\Douyin;

use LiveStream\Enum\Quality;
use LiveStream\Contracts\LiveInterface;

class Live implements LiveInterface
{
    public function __construct(
        protected int $status,
        protected string $title,
        protected string $anchorName,
        protected string $roomId,
        protected array $streamUrl,
        protected string $userCountStr
    ) {}

    public function isLive(): bool
    {
        return $this->status === 2;
    }

    public function getFlvUrl(?Quality $quality = null): ?string
    {
        return $this->streamUrl['flv_pull_url'][$quality->value] ?? null;
    }

    public function getHlsUrl(?Quality $quality = null): ?string
    {
        return $this->streamUrl['hls_pull_url_map'][$quality->value] ?? null;
    }

    public function getStreamUrl(): array
    {
        return $this->streamUrl;
    }

    public function getAnchorName(): string
    {
        return $this->anchorName;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getRoomId(): string
    {
        return $this->roomId;
    }

    public function getUserCountStr(): string
    {
        return $this->userCountStr;
    }
}
