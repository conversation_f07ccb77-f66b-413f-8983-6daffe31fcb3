<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Contracts\PlatformInterface;
use LiveStream\Config\RecordingOptions;
use LiveStream\Exceptions\LiveStreamException;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Traits\HasRecordId;
use LiveStream\Traits\HasPathBuilder;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Enum\Quality;
use InvalidArgumentException;

class PendingRecorder
{
    use HasRecordId;
    use HasPathBuilder;

    private ?LiveInterface $live = null;

    /**
     * 缓存的已验证流URL
     */
    private ?string $validatedFlvUrl = null;
    private ?string $validatedHlsUrl = null;

    /**
     * 选择的清晰度
     */
    private ?Quality $selectedQuality = null;

    protected ?string $savePath = null;

    /**
     * 构造函数
     * 
     * @param PlatformInterface $platform 平台接口
     * @param RecordingOptions $options 录制选项
     * @param string $recordId 录制ID
     * @param bool $enableOverseasOptimization 是否启用海外优化
     * @throws LiveStreamException 当配置构建失败时
     */
    public function __construct(
        private readonly RecordrConnector $recordrConnector,
        private readonly PlatformInterface $platform,
        private readonly bool $enableOverseasOptimization = false,
    ) {}

    /**
     * 获取 RecordrConnector 实例
     * 
     * @return RecordrConnector  RecordrConnector 实例
     */
    public function recordrConnector(): RecordrConnector
    {
        return $this->recordrConnector;
    }

    /**
     * 获取房间信息
     * 
     * @return \LiveStream\Contracts\LiveInterface 房间信息
     */
    public function getLive(): LiveInterface
    {
        return $this->platform->getLive();
    }

    /**
     * 获取平台接口
     * 
     * @return PlatformInterface 平台接口
     */
    public function getPlatform(): PlatformInterface
    {
        return $this->platform;
    }

    /**
     * 是否启用海外优化
     * 
     * @return bool 是否启用海外优化
     */
    public function isOverseasOptimized(): bool
    {
        return $this->enableOverseasOptimization;
    }

    /**
     * 构建输出路径
     * 
     * @return string 输出路径
     * @throws LiveStreamException 当路径构建失败时
     */
    public function savePath(): string
    {
        return $this->savePath ??= $this->buildFilePath();
    }

    /**
     * 获取已验证的流URL
     *
     * @param string $type 流类型：'hls' 或 'flv'
     * @return string|null 已验证的流URL，如果不可用则返回null
     * @throws InvalidArgumentException 当流类型无效时
     */
    public function getValidatedStreamUrl(string $type = 'hls'): ?string
    {
        if (!in_array($type, ['hls', 'flv'], true)) {
            throw new InvalidArgumentException("Invalid stream type: {$type}");
        }

        // 检查缓存
        $cachedUrl = $type === 'hls' ? $this->validatedHlsUrl : $this->validatedFlvUrl;
        if ($cachedUrl !== null) {
            return $cachedUrl;
        }

        // 获取并缓存流URL
        $url = $this->fetchStreamUrl($type);

        if ($type === 'hls') {
            $this->validatedHlsUrl = $url;
        } else {
            $this->validatedFlvUrl = $url;
        }

        return $url;
    }

    /**
     * 获取选择的清晰度
     *
     * @return Quality|null 选择的清晰度，如果未选择则返回null
     */
    public function getSelectedQuality(): ?Quality
    {
        return $this->selectedQuality;
    }

    /**
     * 刷新流URL缓存
     *
     * 清除所有缓存的流URL，强制重新获取
     */
    public function refreshStreamUrls(): void
    {
        $this->validatedFlvUrl = null;
        $this->validatedHlsUrl = null;
        $this->selectedQuality = null;
    }

    /**
     * 获取流URL（内部方法）
     *
     * @param string $type 流类型
     * @return string|null 流URL
     */
    private function fetchStreamUrl(string $type): ?string
    {
        $live = $this->getLive();
        $configuredQuality = $this->recordrConnector()->config()->getQuality();

        // 首先尝试配置的清晰度
        if ($configuredQuality !== null) {
            $url = $type === 'hls'
                ? $live->getHlsUrl($configuredQuality)
                : $live->getFlvUrl($configuredQuality);

            if ($url !== null) {
                $this->selectedQuality = $configuredQuality;
                return $url;
            }
        }

        // 如果配置的清晰度不可用，使用智能降级
        $url = $type === 'hls'
            ? $live->getHlsUrl(null)
            : $live->getFlvUrl(null);

        if ($url !== null) {
            // 尝试确定选择的清晰度
            $this->selectedQuality = $this->determineSelectedQuality($live, $url, $type);
        }

        return $url;
    }

    /**
     * 确定选择的清晰度
     *
     * @param LiveInterface $live Live对象
     * @param string $selectedUrl 选择的URL
     * @param string $type 流类型
     * @return Quality|null 确定的清晰度
     */
    private function determineSelectedQuality(LiveInterface $live, string $selectedUrl, string $type): ?Quality
    {
        $streamUrls = $live->getStreamUrl();
        $urlMapKey = $type === 'hls' ? 'hls_pull_url_map' : 'flv_pull_url';

        if (!isset($streamUrls[$urlMapKey]) || !is_array($streamUrls[$urlMapKey])) {
            return null;
        }

        $urlMap = $streamUrls[$urlMapKey];

        // 按优先级顺序查找匹配的清晰度
        foreach (Quality::getPriorityOrder() as $quality) {
            if (isset($urlMap[$quality->value]) && $urlMap[$quality->value] === $selectedUrl) {
                return $quality;
            }
        }

        return null;
    }

    /**
     * 验证流连接
     *
     * @param string $url 流URL
     * @return bool 连接是否成功
     */
    public function validateStreamConnection(string $url): bool
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'HEAD',
                    'timeout' => 10,
                    'user_agent' => 'LiveStream/1.0',
                    'follow_location' => true,
                    'max_redirects' => 3,
                ],
            ]);

            $headers = @get_headers($url, false, $context);

            if ($headers === false) {
                return false;
            }

            // 检查HTTP状态码
            $statusLine = $headers[0] ?? '';
            return strpos($statusLine, '200') !== false ||
                strpos($statusLine, '302') !== false ||
                strpos($statusLine, '301') !== false;
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * 验证流格式
     *
     * @param string $url 流URL
     * @param string $streamType 流类型
     * @return bool 格式是否有效
     */
    public function validateStreamFormat(string $url, string $streamType): bool
    {
        try {
            // 基本的URL格式检查
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                return false;
            }

            // 检查文件扩展名或URL模式
            switch ($streamType) {
                case 'hls':
                    return strpos($url, '.m3u8') !== false;
                case 'flv':
                    return strpos($url, '.flv') !== false;
                default:
                    return true; // 未知类型，假设有效
            }
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * 构建文件路径
     *
     * @return string 文件路径
     */
    protected function buildFilePath(): string
    {
        $basePath = $this->recordrConnector()->config()->getSavePath();
        $anchorName = $this->sanitizeFilename($this->getLive()->getAnchorName());
        $title = $this->sanitizeFilename($this->getLive()->getTitle());
        $format = $this->recordrConnector()->config()->getFormat()->value;

        $timestamp = date('Y-m-d_H-i-s');
        $date = date('Y-m-d');

        // 使用新的安全路径构建方法
        $filePath = $this->buildSafePath(
            $basePath,
            $anchorName,
            $date,
            $title,
            "{$timestamp}.{$format}"
        );

        // 确保输出目录存在
        $this->ensureOutputDirectoryExists($filePath);

        return $filePath;
    }
}
