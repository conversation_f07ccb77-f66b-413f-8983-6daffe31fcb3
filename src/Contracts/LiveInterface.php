<?php

declare(strict_types=1);

namespace LiveStream\Contracts;

use LiveStream\Enum\Quality;

interface LiveInterface
{
    /**
     * 检查直播状态（对应main.py中的is_live判断）
     *
     * @param array $roomInfo 房间信息
     * @return bool 是否正在直播
     */
    public function isLive(): bool;

    /**
     * 获取主播名称（对应main.py中的anchor_name提取）
     *
     * @param array $roomInfo 房间信息
     * @return string 主播名称
     */
    public function getAnchorName(): string;

    /**
     * 获取直播标题（对应main.py中的title提取）
     *
     * @param array $roomInfo 房间信息
     * @return string 直播标题
     */
    public function getTitle(): string;

    /**
     * 获取房间ID（对应main.py中的room_id提取）
     *
     * @param array $roomInfo 房间信息
     * @return string 房间ID
     */
    public function getRoomId(): string;

    public function getFlvUrl(Quality $quality = Quality::ORIGIN): ?string;

    public function getHlsUrl(Quality $quality = Quality::ORIGIN): ?string;

    public function getStreamUrl(): array;
}
