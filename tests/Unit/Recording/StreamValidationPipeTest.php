<?php

declare(strict_types=1);

use LiveStream\Recording\Pipes\StreamValidationPipe;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Contracts\PlatformInterface;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Enum\Quality;
use LiveStream\Config\RecordingOptions;
use LiveStream\Exceptions\StreamUnavailableException;
use LiveStream\Exceptions\StreamValidationException;

describe('StreamValidationPipe', function () {

    beforeEach(function () {
        $this->mockPlatform = Mockery::mock(PlatformInterface::class);
        $this->mockLive = Mockery::mock(LiveInterface::class);
        $this->mockRecordrConnector = Mockery::mock(RecordrConnector::class);
        $this->mockConfig = Mockery::mock(RecordingOptions::class);
        $this->mockPendingRecorder = Mockery::mock(PendingRecorder::class);

        // 设置基本的 mock 行为
        $this->mockPendingRecorder->shouldReceive('getLive')->andReturn($this->mockLive);
        $this->mockPendingRecorder->shouldReceive('recordrConnector')->andReturn($this->mockRecordrConnector);
        $this->mockRecordrConnector->shouldReceive('config')->andReturn($this->mockConfig);

        $this->mockLive->shouldReceive('isLive')->andReturn(true);
        $this->mockLive->shouldReceive('getAnchorName')->andReturn('TestAnchor');

        // 默认启用流验证
        $this->mockConfig->shouldReceive('isStreamValidationEnabled')->andReturn(true);
        $this->mockConfig->shouldReceive('getMaxValidationRetries')->andReturn(2);
        $this->mockConfig->shouldReceive('getPreferredStreamType')->andReturn('hls');

        $this->pipe = new StreamValidationPipe();
    });

    afterEach(function () {
        Mockery::close();
    });

    describe('HTTP Connection Validation', function () {
        test('should pass when stream URL is accessible', function () {
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/valid-stream.m3u8');

            // 模拟HTTP连接成功
            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->with('https://example.com/valid-stream.m3u8')
                ->once()
                ->andReturn(true);

            // 模拟格式验证成功
            $this->mockPendingRecorder->shouldReceive('validateStreamFormat')
                ->with('https://example.com/valid-stream.m3u8', 'hls')
                ->once()
                ->andReturn(true);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });

        test('should throw exception when stream URL is not accessible', function () {
            // 第一次尝试
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->times(3) // 初始尝试 + 2次重试
                ->andReturn('https://example.com/invalid-stream.m3u8', 'https://example.com/invalid2.m3u8', null);

            // 模拟HTTP连接失败
            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->times(2)
                ->andReturn(false, false);

            // 模拟刷新流URL
            $this->mockPendingRecorder->shouldReceive('refreshStreamUrls')
                ->times(2);

            // 尝试备用类型（FLV）
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('flv')
                ->times(3)
                ->andReturn(null, null, null);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $this->pipe->handle($this->mockPendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class, 'No valid stream URL available');
        });

        test('should retry with different quality when HTTP validation fails', function () {
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/origin-stream.m3u8');

            // 第一次验证失败
            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->with('https://example.com/origin-stream.m3u8')
                ->once()
                ->andReturn(false);

            // 刷新流URL（触发降级）
            $this->mockPendingRecorder->shouldReceive('refreshStreamUrls')
                ->once();

            // 获取降级后的URL
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/hd-stream.m3u8');

            // 第二次验证成功
            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->with('https://example.com/hd-stream.m3u8')
                ->once()
                ->andReturn(true);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });
    });

    describe('Stream Format Validation', function () {
        test('should validate HLS stream format', function () {
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/stream.m3u8');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->once()
                ->andReturn(true);

            // 模拟HLS格式验证
            $this->mockPendingRecorder->shouldReceive('validateStreamFormat')
                ->with('https://example.com/stream.m3u8', 'hls')
                ->once()
                ->andReturn(true);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });

        test('should validate FLV stream format', function () {
            $this->mockConfig->shouldReceive('getPreferredStreamType')
                ->andReturn('flv');

            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('flv')
                ->once()
                ->andReturn('https://example.com/stream.flv');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->once()
                ->andReturn(true);

            $this->mockPendingRecorder->shouldReceive('validateStreamFormat')
                ->with('https://example.com/stream.flv', 'flv')
                ->once()
                ->andReturn(true);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });

        test('should throw exception when stream format is invalid', function () {
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/invalid-format.m3u8');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->once()
                ->andReturn(true);

            $this->mockPendingRecorder->shouldReceive('validateStreamFormat')
                ->with('https://example.com/invalid-format.m3u8', 'hls')
                ->once()
                ->andReturn(false);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $this->pipe->handle($this->mockPendingRecorder, $next))
                ->toThrow(StreamValidationException::class, 'Invalid stream format');
        });
    });

    describe('Quality Degradation Retry', function () {
        test('should try multiple qualities until finding valid stream', function () {
            // 第一次尝试：原画失败
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/origin.m3u8');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->with('https://example.com/origin.m3u8')
                ->once()
                ->andReturn(false);

            $this->mockPendingRecorder->shouldReceive('refreshStreamUrls')
                ->once();

            // 第二次尝试：高清成功
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn('https://example.com/hd.m3u8');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->with('https://example.com/hd.m3u8')
                ->once()
                ->andReturn(true);

            $this->mockPendingRecorder->shouldReceive('validateStreamFormat')
                ->with('https://example.com/hd.m3u8', 'hls')
                ->once()
                ->andReturn(true);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });

        test('should throw exception when all qualities fail', function () {
            // 模拟所有清晰度都失败的情况
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->times(3) // 假设最多重试3次
                ->andReturn(
                    'https://example.com/origin.m3u8',
                    'https://example.com/hd.m3u8',
                    null // 最后返回null表示没有更多清晰度
                );

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->times(2)
                ->andReturn(false, false);

            $this->mockPendingRecorder->shouldReceive('refreshStreamUrls')
                ->times(2);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $this->pipe->handle($this->mockPendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class, 'No valid stream URL available after quality degradation');
        });
    });

    describe('Stream Type Fallback', function () {
        test('should fallback from HLS to FLV when HLS fails', function () {
            // HLS失败
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn(null);

            // 尝试FLV
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('flv')
                ->once()
                ->andReturn('https://example.com/stream.flv');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->with('https://example.com/stream.flv')
                ->once()
                ->andReturn(true);

            $this->mockPendingRecorder->shouldReceive('validateStreamFormat')
                ->with('https://example.com/stream.flv', 'flv')
                ->once()
                ->andReturn(true);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });

        test('should throw exception when both HLS and FLV fail', function () {
            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->once()
                ->andReturn(null);

            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('flv')
                ->once()
                ->andReturn(null);

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $this->pipe->handle($this->mockPendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class, 'No valid stream URL available');
        });
    });

    describe('Configuration Options', function () {
        test('should respect max retry attempts configuration', function () {
            $this->mockConfig->shouldReceive('getMaxValidationRetries')
                ->andReturn(1); // 只允许重试1次

            $this->mockPendingRecorder->shouldReceive('getValidatedStreamUrl')
                ->with('hls')
                ->times(2) // 初始尝试 + 1次重试
                ->andReturn('https://example.com/stream1.m3u8', 'https://example.com/stream2.m3u8');

            $this->mockPendingRecorder->shouldReceive('validateStreamConnection')
                ->times(2)
                ->andReturn(false, false);

            $this->mockPendingRecorder->shouldReceive('refreshStreamUrls')
                ->once();

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            expect(fn() => $this->pipe->handle($this->mockPendingRecorder, $next))
                ->toThrow(StreamUnavailableException::class);
        });

        test('should skip validation when disabled in config', function () {
            $this->mockConfig->shouldReceive('isStreamValidationEnabled')
                ->andReturn(false);

            // 当验证被禁用时，不应该调用任何验证方法
            $this->mockPendingRecorder->shouldNotReceive('getValidatedStreamUrl');
            $this->mockPendingRecorder->shouldNotReceive('validateStreamConnection');

            $next = function ($pendingRecorder) {
                return $pendingRecorder;
            };

            $result = $this->pipe->handle($this->mockPendingRecorder, $next);
            expect($result)->toBe($this->mockPendingRecorder);
        });
    });
});
