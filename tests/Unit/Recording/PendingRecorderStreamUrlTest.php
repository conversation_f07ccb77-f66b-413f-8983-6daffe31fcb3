<?php

declare(strict_types=1);

use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Contracts\PlatformInterface;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;
use LiveStream\Config\RecordingOptions;

describe('PendingRecorder Stream URL Management', function () {

    beforeEach(function () {
        $this->mockPlatform = Mockery::mock(PlatformInterface::class);
        $this->mockLive = Mockery::mock(LiveInterface::class);
        $this->mockRecordrConnector = Mockery::mock(RecordrConnector::class);
        $this->mockConfig = Mockery::mock(RecordingOptions::class);

        // 设置基本的 mock 行为
        $this->mockPlatform->shouldReceive('getLive')->andReturn($this->mockLive);
        $this->mockPlatform->shouldReceive('getReferer')->andReturn('https://example.com');
        $this->mockPlatform->shouldReceive('getPlatformName')->andReturn('test');
        $this->mockPlatform->shouldReceive('supportsUrl')->andReturn(true);

        $this->mockLive->shouldReceive('isLive')->andReturn(true);
        $this->mockLive->shouldReceive('getAnchorName')->andReturn('TestAnchor');
        $this->mockLive->shouldReceive('getTitle')->andReturn('Test Title');
        $this->mockLive->shouldReceive('getRoomId')->andReturn('123456');

        $this->mockRecordrConnector->shouldReceive('config')->andReturn($this->mockConfig);
        $this->mockConfig->shouldReceive('getQuality')->andReturn(null); // 默认使用智能降级
        $this->mockConfig->shouldReceive('getSavePath')->andReturn('/tmp');

        // 使用真实的 OutputFormat 枚举值
        $this->mockConfig->shouldReceive('getFormat')->andReturn(OutputFormat::MP4);
    });

    afterEach(function () {
        Mockery::close();
    });

    describe('Stream URL Caching', function () {
        test('should cache validated stream URLs', function () {
            // 配置为null，直接使用智能降级
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn('https://example.com/stream.m3u8');

            $this->mockLive->shouldReceive('getFlvUrl')
                ->with(null)
                ->once()
                ->andReturn('https://example.com/stream.flv');

            // 为 determineSelectedQuality 方法添加 mock
            $this->mockLive->shouldReceive('getStreamUrl')
                ->twice()
                ->andReturn([
                    'hls_pull_url_map' => ['HD1' => 'https://example.com/stream.m3u8'],
                    'flv_pull_url' => ['HD1' => 'https://example.com/stream.flv']
                ]);

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            // 第一次调用应该从 Live 对象获取
            $hlsUrl1 = $pendingRecorder->getValidatedStreamUrl('hls');
            $flvUrl1 = $pendingRecorder->getValidatedStreamUrl('flv');

            // 第二次调用应该从缓存获取（不再调用 Live 对象）
            $hlsUrl2 = $pendingRecorder->getValidatedStreamUrl('hls');
            $flvUrl2 = $pendingRecorder->getValidatedStreamUrl('flv');

            expect($hlsUrl1)->toBe('https://example.com/stream.m3u8');
            expect($flvUrl1)->toBe('https://example.com/stream.flv');
            expect($hlsUrl2)->toBe($hlsUrl1);
            expect($flvUrl2)->toBe($flvUrl1);
        });

        test('should return null when no stream URL available', function () {
            // 配置为null，直接使用智能降级
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn(null);

            $this->mockLive->shouldReceive('getFlvUrl')
                ->with(null)
                ->once()
                ->andReturn(null);

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            expect($pendingRecorder->getValidatedStreamUrl('hls'))->toBeNull();
            expect($pendingRecorder->getValidatedStreamUrl('flv'))->toBeNull();
        });

        test('should cache selected quality', function () {
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn('https://example.com/hd.m3u8');

            // 模拟智能降级选择了 HD1 清晰度
            $this->mockLive->shouldReceive('getStreamUrl')
                ->once()
                ->andReturn([
                    'hls_pull_url_map' => [
                        'HD1' => 'https://example.com/hd.m3u8'
                    ]
                ]);

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            $pendingRecorder->getValidatedStreamUrl('hls');

            // 应该能获取到选择的清晰度
            expect($pendingRecorder->getSelectedQuality())->toBe(Quality::HD1);
        });
    });

    describe('Stream URL Refresh', function () {
        test('should refresh cached URLs when requested', function () {
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->twice() // 第一次获取 + 刷新后再次获取
                ->andReturn('https://example.com/stream1.m3u8', 'https://example.com/stream2.m3u8');

            // 为 determineSelectedQuality 方法添加 mock
            $this->mockLive->shouldReceive('getStreamUrl')
                ->twice()
                ->andReturn([
                    'hls_pull_url_map' => ['HD1' => 'https://example.com/stream1.m3u8']
                ], [
                    'hls_pull_url_map' => ['HD1' => 'https://example.com/stream2.m3u8']
                ]);

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            // 第一次获取
            $url1 = $pendingRecorder->getValidatedStreamUrl('hls');
            expect($url1)->toBe('https://example.com/stream1.m3u8');

            // 刷新缓存
            $pendingRecorder->refreshStreamUrls();

            // 再次获取应该得到新的URL
            $url2 = $pendingRecorder->getValidatedStreamUrl('hls');
            expect($url2)->toBe('https://example.com/stream2.m3u8');
        });

        test('should clear selected quality when refreshing', function () {
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->twice()
                ->andReturn('https://example.com/hd.m3u8', 'https://example.com/origin.m3u8');

            $this->mockLive->shouldReceive('getStreamUrl')
                ->twice()
                ->andReturn(
                    ['hls_pull_url_map' => ['HD1' => 'https://example.com/hd.m3u8']],
                    ['hls_pull_url_map' => ['ORIGIN' => 'https://example.com/origin.m3u8']]
                );

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            // 第一次获取
            $pendingRecorder->getValidatedStreamUrl('hls');
            expect($pendingRecorder->getSelectedQuality())->toBe(Quality::HD1);

            // 刷新后应该重新选择清晰度
            $pendingRecorder->refreshStreamUrls();
            $pendingRecorder->getValidatedStreamUrl('hls');
            expect($pendingRecorder->getSelectedQuality())->toBe(Quality::ORIGIN);
        });
    });

    describe('Smart Quality Degradation Integration', function () {
        test('should use smart degradation when quality is null', function () {
            // 模拟智能降级：原画不可用，降级到超清
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn('https://example.com/fullhd.m3u8'); // 返回超清URL

            // 为 determineSelectedQuality 方法添加 mock
            $this->mockLive->shouldReceive('getStreamUrl')
                ->once()
                ->andReturn([
                    'hls_pull_url_map' => ['FULL_HD1' => 'https://example.com/fullhd.m3u8']
                ]);

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            $url = $pendingRecorder->getValidatedStreamUrl('hls');
            expect($url)->toBe('https://example.com/fullhd.m3u8');
        });

        test('should respect configured quality when specified', function () {
            // 创建一个新的 mock config 来覆盖默认设置
            $mockConfigWithQuality = Mockery::mock(RecordingOptions::class);
            $mockConfigWithQuality->shouldReceive('getQuality')->andReturn(Quality::HD1);
            $mockConfigWithQuality->shouldReceive('getSavePath')->andReturn('/tmp');
            $mockConfigWithQuality->shouldReceive('getFormat')->andReturn(OutputFormat::MP4);

            $mockConnectorWithQuality = Mockery::mock(RecordrConnector::class);
            $mockConnectorWithQuality->shouldReceive('config')->andReturn($mockConfigWithQuality);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(Quality::HD1)
                ->once()
                ->andReturn('https://example.com/hd.m3u8');

            $pendingRecorder = new PendingRecorder($mockConnectorWithQuality, $this->mockPlatform);

            $url = $pendingRecorder->getValidatedStreamUrl('hls');
            expect($url)->toBe('https://example.com/hd.m3u8');
        });

        test('should fallback to smart degradation when configured quality unavailable', function () {
            // 创建一个新的 mock config 来覆盖默认设置
            $mockConfigWithOrigin = Mockery::mock(RecordingOptions::class);
            $mockConfigWithOrigin->shouldReceive('getQuality')->andReturn(Quality::ORIGIN);
            $mockConfigWithOrigin->shouldReceive('getSavePath')->andReturn('/tmp');
            $mockConfigWithOrigin->shouldReceive('getFormat')->andReturn(OutputFormat::MP4);

            $mockConnectorWithOrigin = Mockery::mock(RecordrConnector::class);
            $mockConnectorWithOrigin->shouldReceive('config')->andReturn($mockConfigWithOrigin);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(Quality::ORIGIN)
                ->once()
                ->andReturn(null); // 指定清晰度不可用

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn('https://example.com/hd.m3u8'); // 智能降级返回高清

            // 为 determineSelectedQuality 方法添加 mock
            $this->mockLive->shouldReceive('getStreamUrl')
                ->once()
                ->andReturn([
                    'hls_pull_url_map' => ['HD1' => 'https://example.com/hd.m3u8']
                ]);

            $pendingRecorder = new PendingRecorder($mockConnectorWithOrigin, $this->mockPlatform);

            $url = $pendingRecorder->getValidatedStreamUrl('hls');
            expect($url)->toBe('https://example.com/hd.m3u8');
        });
    });

    describe('Error Handling', function () {
        test('should handle invalid stream type gracefully', function () {
            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            expect(fn() => $pendingRecorder->getValidatedStreamUrl('invalid'))
                ->toThrow(InvalidArgumentException::class, 'Invalid stream type: invalid');
        });

        test('should handle Live interface exceptions', function () {
            $this->mockLive->shouldReceive('getHlsUrl')
                ->andThrow(new RuntimeException('Stream fetch failed'));

            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            expect(fn() => $pendingRecorder->getValidatedStreamUrl('hls'))
                ->toThrow(RuntimeException::class, 'Stream fetch failed');
        });
    });

    describe('Backward Compatibility', function () {
        test('should maintain existing getLive() behavior', function () {
            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            // 现有的 getLive() 方法应该继续工作
            $live = $pendingRecorder->getLive();
            expect($live)->toBe($this->mockLive);
        });

        test('should not affect existing savePath() functionality', function () {
            $pendingRecorder = new PendingRecorder($this->mockRecordrConnector, $this->mockPlatform);

            // savePath() 方法应该继续正常工作
            $path = $pendingRecorder->savePath();
            expect($path)->toBeString();
            expect($path)->toContain('TestAnchor');
        });
    });
});
