<?php

declare(strict_types=1);

use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Recording\Contracts\RecorderInterface;
use LiveStream\Contracts\PlatformInterface;
use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\Config\StreamConfig;
use LiveStream\Exceptions\NetworkTimeoutException;
use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Exceptions\InvalidConfigurationException;

beforeEach(function () {
    $this->mockPlatform = Mockery::mock(PlatformInterface::class);
    $this->mockRoomInfo = Mockery::mock(RoomInfoInterface::class);
    $this->mockStreamConfig = Mockery::mock(StreamConfig::class);
    $this->mockRecorder = Mockery::mock(RecorderInterface::class);

    // 设置默认行为
    $this->mockRoomInfo->shouldReceive('isLive')->andReturn(true);
    $this->mockRoomInfo->shouldReceive('getAnchorName')->andReturn('TestAnchor');
    $this->mockRoomInfo->shouldReceive('getStreamConfig')->andReturn($this->mockStreamConfig);
    $this->mockPlatform->shouldReceive('getRoomInfo')->andReturn($this->mockRoomInfo);
    $this->mockPlatform->shouldReceive('getReferer')->andReturn('https://example.com');

    $this->mockStreamConfig->shouldReceive('getRecordUrl')->andReturn('http://stream.test/live');
});

afterEach(function () {
    Mockery::close();
});

test('默认不进行重试', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);

    $this->mockRecorder->shouldReceive('start')
        ->once()
        ->andThrow(new NetworkTimeoutException('Network timeout after 30 seconds'));

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(NetworkTimeoutException::class);
});

test('可以配置重试次数', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 设置最多尝试3次

    $attemptCount = 0;
    $this->mockRecorder->shouldReceive('start')
        ->times(3) // 总共3次尝试
        ->andReturnUsing(function () use (&$attemptCount) {
            $attemptCount++;
            if ($attemptCount < 3) {
                throw new NetworkTimeoutException('Network timeout after 30 seconds');
            }
            return 'success'; // 简单返回一个成功标记
        });

    $result = $connector->handle($this->mockPlatform);

    expect($result)->toBe('success');
    expect($attemptCount)->toBe(3);
});

test('在达到最大重试次数后抛出异常', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(2); // 设置重试2次

    $attemptCount = 0;
    $this->mockRecorder->shouldReceive('start')
        ->andReturnUsing(function () use (&$attemptCount) {
            $attemptCount++;
            throw new NetworkTimeoutException('Network timeout after 30 seconds');
        });

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(NetworkTimeoutException::class);

    // 验证尝试了2次（不是3次，因为 maxTries=2 意味着最多尝试2次）
    expect($attemptCount)->toBe(2);
});

test('可以使用自定义回调决定是否重试', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 设置重试3次
    $connector->withShouldRetry(function (\Throwable $exception, int $attempt) {
        // 只重试 NetworkTimeoutException
        return $exception instanceof NetworkTimeoutException;
    });

    $this->mockRecorder->shouldReceive('start')
        ->once()
        ->andThrow(new InvalidConfigurationException('Invalid format')); // 不应该重试

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(InvalidConfigurationException::class, 'Invalid format');
});

test('使用自定义回调进行条件重试', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);

    $attempts = [];
    $connector->withTries(5); // 设置最多尝试5次
    $connector->withShouldRetry(function (\Throwable $exception, int $attempt) use (&$attempts) {
        $attempts[] = $attempt;
        // 只在前2次失败后重试（即最多尝试3次）
        return $attempt <= 2;
    });

    $this->mockRecorder->shouldReceive('start')
        ->times(3) // 总共3次尝试（1st attempt + 2 retries）
        ->andThrow(new NetworkTimeoutException('Network timeout after 30 seconds'));

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(NetworkTimeoutException::class);
    expect($attempts)->toBe([1, 2, 3]);
});

test('可以配置重试延迟', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(2); // 设置最多尝试2次
    $connector->withRetryInterval(100); // 100ms

    $startTime = microtime(true);

    $this->mockRecorder->shouldReceive('start')
        ->times(2) // 总共2次尝试
        ->andThrow(new NetworkTimeoutException('Network timeout after 30 seconds'));

    try {
        $connector->handle($this->mockPlatform);
    } catch (NetworkTimeoutException $e) {
        $elapsedTime = (microtime(true) - $startTime) * 1000; // 转换为毫秒
        // 只有第二次尝试前有延迟，所以应该至少延迟了 100ms
        expect($elapsedTime)->toBeGreaterThan(90); // 留一点余地
    }
});

test('可以使用指数退避策略', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 设置最多尝试3次
    $connector->withRetryInterval(100); // 基础延迟 100ms
    $connector->withExponentialBackoff(true); // 启用指数退避

    $delays = [];
    $lastTime = microtime(true);

    $this->mockRecorder->shouldReceive('start')
        ->times(3) // 总共3次尝试
        ->andReturnUsing(function () use (&$delays, &$lastTime) {
            $currentTime = microtime(true);
            $delays[] = ($currentTime - $lastTime) * 1000; // ms
            $lastTime = $currentTime;
            throw new NetworkTimeoutException('Network timeout after 30 seconds');
        });

    try {
        $connector->handle($this->mockPlatform);
    } catch (NetworkTimeoutException $e) {
        // 第一次没有延迟，第二次延迟 100ms，第三次延迟 200ms
        expect($delays[1])->toBeGreaterThan(90)->toBeLessThan(110);
        expect($delays[2])->toBeGreaterThan(190)->toBeLessThan(210);
    }
});

test('每次重试都会重新创建 PendingRecorder', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(2); // 设置最多尝试2次

    $recorderInstances = [];

    // 使用中间件来捕获 PendingRecorder 实例
    $connector->middleware()->pipe(function (PendingRecorder $pendingRecorder, \Closure $next) use (&$recorderInstances) {
        $recorderInstances[] = spl_object_id($pendingRecorder);
        return $next($pendingRecorder);
    });

    $this->mockRecorder->shouldReceive('start')
        ->times(2) // 总共2次尝试
        ->andThrow(new NetworkTimeoutException('Network timeout after 30 seconds'));

    try {
        $connector->handle($this->mockPlatform);
    } catch (NetworkTimeoutException $e) {
        // 应该有2个不同的实例ID
        expect(count($recorderInstances))->toBe(2);
        expect(count(array_unique($recorderInstances)))->toBe(2);
    }
});

test('直播未开始时抛出 StreamNotLiveException', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);

    // 重新创建 mock 并设置直播未开始
    $this->mockRoomInfo = Mockery::mock(RoomInfoInterface::class);
    $this->mockRoomInfo->shouldReceive('isLive')->andReturn(false);
    $this->mockRoomInfo->shouldReceive('getAnchorName')->andReturn('TestAnchor');
    $this->mockRoomInfo->shouldReceive('getStreamConfig')->andReturn($this->mockStreamConfig);

    $this->mockPlatform = Mockery::mock(PlatformInterface::class);
    $this->mockPlatform->shouldReceive('getRoomInfo')->andReturn($this->mockRoomInfo);
    $this->mockPlatform->shouldReceive('getReferer')->andReturn('https://example.com');

    // mockRecorder 不应该被调用，因为在验证阶段就会抛出异常
    $this->mockRecorder->shouldReceive('start')->never();

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(StreamNotLiveException::class, 'TestAnchor Stream is not live');
});

test('可以通过自定义回调阻止特定异常重试', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 设置重试3次

    // 通过回调设置 StreamNotLiveException 不重试
    $connector->withShouldRetry(function (\Throwable $exception, int $attempt) {
        // StreamNotLiveException 不应该重试
        return !($exception instanceof StreamNotLiveException);
    });

    // 重新创建 mock 并设置直播未开始
    $this->mockRoomInfo = Mockery::mock(RoomInfoInterface::class);
    $this->mockRoomInfo->shouldReceive('isLive')->andReturn(false);
    $this->mockRoomInfo->shouldReceive('getAnchorName')->andReturn('TestAnchor');
    $this->mockRoomInfo->shouldReceive('getStreamConfig')->andReturn($this->mockStreamConfig);

    $this->mockPlatform = Mockery::mock(PlatformInterface::class);
    $this->mockPlatform->shouldReceive('getRoomInfo')->andReturn($this->mockRoomInfo);
    $this->mockPlatform->shouldReceive('getReferer')->andReturn('https://example.com');

    // mockRecorder 不应该被调用
    $this->mockRecorder->shouldReceive('start')->never();

    // 中间件只应该被调用一次
    $middlewareCallCount = 0;
    $connector->middleware()->pipe(function (PendingRecorder $pendingRecorder, \Closure $next) use (&$middlewareCallCount) {
        $middlewareCallCount++;
        return $next($pendingRecorder);
    });

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(StreamNotLiveException::class);

    // 验证中间件只被调用了一次（没有重试）
    expect($middlewareCallCount)->toBe(1);
});

test('重试时使用正确的延迟策略', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 设置最多尝试3次
    $connector->withRetryInterval(50); // 50ms 基础延迟
    $connector->withExponentialBackoff(false); // 禁用指数退避

    $timestamps = [];
    $this->mockRecorder->shouldReceive('start')
        ->times(3) // 总共3次尝试
        ->andReturnUsing(function () use (&$timestamps) {
            $timestamps[] = microtime(true);
            throw new NetworkTimeoutException('Network timeout');
        });

    try {
        $connector->handle($this->mockPlatform);
    } catch (NetworkTimeoutException $e) {
        // 计算每次尝试之间的延迟
        $delays = [];
        for ($i = 1; $i < count($timestamps); $i++) {
            $delays[] = ($timestamps[$i] - $timestamps[$i - 1]) * 1000; // 转换为毫秒
        }

        // 第一次尝试没有延迟，后续每次延迟应该接近50ms
        expect($delays[0])->toBeGreaterThan(45)->toBeLessThan(55);
        expect($delays[1])->toBeGreaterThan(45)->toBeLessThan(55);
    }
});

test('HTTP 404 Not Found 错误会触发重试', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 最多尝试3次

    // 仅当异常消息包含 404 Not Found 时重试
    $connector->withShouldRetry(function (\Throwable $exception, int $attempt) {
        return stripos($exception->getMessage(), '404 Not Found') !== false;
    });

    $attemptCount = 0;
    $this->mockRecorder->shouldReceive('start')
        ->times(3) // 前两次抛出 404，第三次成功
        ->andReturnUsing(function () use (&$attemptCount) {
            $attemptCount++;
            if ($attemptCount < 3) {
                throw new \RuntimeException('HTTP error 404 Not Found');
            }
            return 'success';
        });

    $result = $connector->handle($this->mockPlatform);

    expect($result)->toBe('success');
    expect($attemptCount)->toBe(3);
});

test('ExecutionFailureException 会按异常类型触发重试并成功', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(3); // 最多尝试3次

    // 基于异常类型判断（而不是 message）
    $connector->withShouldRetry(function (\Throwable $exception, int $attempt) {
        return $exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException;
    });

    $attemptCount = 0;
    $this->mockRecorder->shouldReceive('start')
        ->times(3) // 前两次抛出 ExecutionFailureException，第三次成功
        ->andReturnUsing(function () use (&$attemptCount) {
            $attemptCount++;
            if ($attemptCount < 3) {
                throw new \Alchemy\BinaryDriver\Exception\ExecutionFailureException(
                    'ffmpeg',
                    "ffmpeg -i input",
                    'HTTP error 404 Not Found'
                );
            }
            return 'success';
        });

    $result = $connector->handle($this->mockPlatform);

    expect($result)->toBe('success');
    expect($attemptCount)->toBe(3);
});

test('ExecutionFailureException 达到最大重试后仍抛出', function () {
    $connector = new RecordrConnector();
    $connector->withRecordr($this->mockRecorder);
    $connector->withTries(2); // 最多尝试2次

    // 基于异常类型判断（而不是 message）
    $connector->withShouldRetry(function (\Throwable $exception, int $attempt) {
        return $exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException;
    });

    $attemptCount = 0;
    $this->mockRecorder->shouldReceive('start')
        ->times(2)
        ->andReturnUsing(function () use (&$attemptCount) {
            $attemptCount++;
            throw new \Alchemy\BinaryDriver\Exception\ExecutionFailureException(
                'ffmpeg',
                "ffmpeg -i input",
                'HTTP error 404 Not Found'
            );
        });

    expect(fn() => $connector->handle($this->mockPlatform))
        ->toThrow(\Alchemy\BinaryDriver\Exception\ExecutionFailureException::class);

    expect($attemptCount)->toBe(2);
});
