<?php

declare(strict_types=1);

use LiveStream\Traits\HasStreamUrl;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Enum\Quality;
use LiveStream\Stream\Stream;
use LiveStream\Config\RecordingOptions;

describe('HasStreamUrl Trait', function () {

    beforeEach(function () {
        // 创建一个使用HasStreamUrl trait的测试类
        $this->testClass = new class {
            use HasStreamUrl;

            private $mockLive;
            private $mockConfig;

            public function setMockLive($live)
            {
                $this->mockLive = $live;
            }

            public function setMockConfig($config)
            {
                $this->mockConfig = $config;
            }

            // 模拟获取Live对象的方法
            protected function getLive(): LiveInterface
            {
                return $this->mockLive;
            }

            // 模拟获取配置的方法
            protected function getConfig(): RecordingOptions
            {
                return $this->mockConfig;
            }
        };

        // 创建mock对象
        $this->mockLive = Mockery::mock(LiveInterface::class);
        $this->mockConfig = Mockery::mock(RecordingOptions::class);

        $this->testClass->setMockLive($this->mockLive);
        $this->testClass->setMockConfig($this->mockConfig);

        // 设置基本的mock行为
        $this->mockLive->shouldReceive('isLive')->andReturn(true);
        $this->mockLive->shouldReceive('getAnchorName')->andReturn('TestAnchor');
        $this->mockLive->shouldReceive('getTitle')->andReturn('Test Title');
        $this->mockLive->shouldReceive('getRoomId')->andReturn('123456');
    });

    afterEach(function () {
        Mockery::close();
    });

    describe('Stream URL Caching', function () {
        test('should cache validated stream objects', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            // 创建mock Stream对象
            $mockHlsStream = Mockery::mock(Stream::class);
            $mockHlsStream->shouldReceive('getUrl')->andReturn('https://example.com/stream.m3u8');
            $mockHlsStream->shouldReceive('getType')->andReturn('hls');
            $mockHlsStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $mockFlvStream = Mockery::mock(Stream::class);
            $mockFlvStream->shouldReceive('getUrl')->andReturn('https://example.com/stream.flv');
            $mockFlvStream->shouldReceive('getType')->andReturn('flv');
            $mockFlvStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            // 设置Live对象返回Stream对象
            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn($mockHlsStream);

            $this->mockLive->shouldReceive('getFlvUrl')
                ->with(null)
                ->once()
                ->andReturn($mockFlvStream);

            // 第一次调用应该从Live对象获取
            $hlsStream1 = $this->testClass->getValidatedStreamUrl('hls');
            $flvStream1 = $this->testClass->getValidatedStreamUrl('flv');

            // 第二次调用应该从缓存获取（不再调用Live对象）
            $hlsStream2 = $this->testClass->getValidatedStreamUrl('hls');
            $flvStream2 = $this->testClass->getValidatedStreamUrl('flv');

            expect($hlsStream1)->toBe($mockHlsStream);
            expect($flvStream1)->toBe($mockFlvStream);
            expect($hlsStream2)->toBe($hlsStream1);
            expect($flvStream2)->toBe($flvStream1);
        });

        test('should return null when no stream available', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn(null);

            $this->mockLive->shouldReceive('getFlvUrl')
                ->with(null)
                ->once()
                ->andReturn(null);

            expect($this->testClass->getValidatedStreamUrl('hls'))->toBeNull();
            expect($this->testClass->getValidatedStreamUrl('flv'))->toBeNull();
        });

        test('should cache selected quality', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $mockStream = Mockery::mock(Stream::class);
            $mockStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn($mockStream);

            $this->testClass->getValidatedStreamUrl('hls');

            expect($this->testClass->getSelectedQuality())->toBe(Quality::HD1);
        });
    });

    describe('Stream URL Refresh', function () {
        test('should refresh cached streams when requested', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $mockStream1 = Mockery::mock(Stream::class);
            $mockStream1->shouldReceive('getUrl')->andReturn('https://example.com/stream1.m3u8');
            $mockStream1->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $mockStream2 = Mockery::mock(Stream::class);
            $mockStream2->shouldReceive('getUrl')->andReturn('https://example.com/stream2.m3u8');
            $mockStream2->shouldReceive('getQuality')->andReturn(Quality::ORIGIN);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->twice()
                ->andReturn($mockStream1, $mockStream2);

            // 第一次获取
            $stream1 = $this->testClass->getValidatedStreamUrl('hls');
            expect($stream1)->toBe($mockStream1);

            // 刷新缓存
            $this->testClass->refreshStreamUrls();

            // 再次获取应该得到新的Stream
            $stream2 = $this->testClass->getValidatedStreamUrl('hls');
            expect($stream2)->toBe($mockStream2);
        });

        test('should clear selected quality when refreshing', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $mockStream1 = Mockery::mock(Stream::class);
            $mockStream1->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $mockStream2 = Mockery::mock(Stream::class);
            $mockStream2->shouldReceive('getQuality')->andReturn(Quality::ORIGIN);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->twice()
                ->andReturn($mockStream1, $mockStream2);

            // 第一次获取
            $this->testClass->getValidatedStreamUrl('hls');
            expect($this->testClass->getSelectedQuality())->toBe(Quality::HD1);

            // 刷新后应该重新选择清晰度
            $this->testClass->refreshStreamUrls();
            $this->testClass->getValidatedStreamUrl('hls');
            expect($this->testClass->getSelectedQuality())->toBe(Quality::ORIGIN);
        });
    });

    describe('Quality Configuration', function () {
        test('should respect configured quality when specified', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $mockStream = Mockery::mock(Stream::class);
            $mockStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(Quality::HD1)
                ->once()
                ->andReturn($mockStream);

            $stream = $this->testClass->getValidatedStreamUrl('hls');
            expect($stream)->toBe($mockStream);
            expect($this->testClass->getSelectedQuality())->toBe(Quality::HD1);
        });

        test('should fallback to smart degradation when configured quality unavailable', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(Quality::ORIGIN);

            $mockStream = Mockery::mock(Stream::class);
            $mockStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(Quality::ORIGIN)
                ->once()
                ->andReturn(null); // 指定清晰度不可用

            $this->mockLive->shouldReceive('getHlsUrl')
                ->with(null)
                ->once()
                ->andReturn($mockStream); // 智能降级返回高清

            $stream = $this->testClass->getValidatedStreamUrl('hls');
            expect($stream)->toBe($mockStream);
            expect($this->testClass->getSelectedQuality())->toBe(Quality::HD1);
        });
    });

    describe('Error Handling', function () {
        test('should handle invalid stream type gracefully', function () {
            expect(fn() => $this->testClass->getValidatedStreamUrl('invalid'))
                ->toThrow(InvalidArgumentException::class, 'Invalid stream type: invalid');
        });

        test('should handle Live interface exceptions', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $this->mockLive->shouldReceive('getHlsUrl')
                ->andThrow(new RuntimeException('Stream fetch failed'));

            expect(fn() => $this->testClass->getValidatedStreamUrl('hls'))
                ->toThrow(RuntimeException::class, 'Stream fetch failed');
        });
    });

    describe('Stream Type Support', function () {
        test('should support both HLS and FLV stream types', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $mockHlsStream = Mockery::mock(Stream::class);
            $mockHlsStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $mockFlvStream = Mockery::mock(Stream::class);
            $mockFlvStream->shouldReceive('getQuality')->andReturn(Quality::HD1);

            $this->mockLive->shouldReceive('getHlsUrl')->andReturn($mockHlsStream);
            $this->mockLive->shouldReceive('getFlvUrl')->andReturn($mockFlvStream);

            expect($this->testClass->getValidatedStreamUrl('hls'))->toBe($mockHlsStream);
            expect($this->testClass->getValidatedStreamUrl('flv'))->toBe($mockFlvStream);
        });

        test('should default to HLS when no type specified', function () {
            $this->mockConfig->shouldReceive('getQuality')->andReturn(null);

            $mockStream = Mockery::mock(Stream::class);
            $mockStream->shouldReceive('getQuality')->andReturn(Quality::HD1);
            $this->mockLive->shouldReceive('getHlsUrl')->andReturn($mockStream);

            expect($this->testClass->getValidatedStreamUrl())->toBe($mockStream);
        });
    });
});
